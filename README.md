# AI遥感智能分析系统

一个基于深度学习的遥感影像智能分析系统，支持土地利用分类、变化检测、目标识别等功能。

## 🚀 主要功能

- **土地利用分类**：自动识别农田、城市、森林、水体等土地类型
- **变化检测**：对比不同时期影像，检测土地利用变化
- **目标识别**：识别建筑物、道路、车辆等特定目标
- **智能分析报告**：生成专业的分析报告和统计数据
- **Web可视化界面**：直观的地图展示和交互操作
- **RESTful API**：支持批量处理和第三方系统集成

## 🛠️ 技术栈

### 后端
- **Python 3.9+**
- **PyTorch/TensorFlow** - 深度学习框架
- **FastAPI** - 高性能API框架
- **GDAL/Rasterio** - 遥感数据处理
- **PostgreSQL + PostGIS** - 空间数据库

### 前端
- **React + TypeScript**
- **Leaflet** - 地图可视化
- **Ant Design** - UI组件库

### 部署
- **Docker** - 容器化部署
- **Nginx** - 反向代理
- **Redis** - 缓存和任务队列

## 📦 安装和运行

### 1. 环境准备

```bash
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境 (Windows)
venv\Scripts\activate

# 激活虚拟环境 (Linux/Mac)
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt
```

### 2. 数据库配置

```bash
# 安装PostgreSQL和PostGIS
# 创建数据库
createdb gis_analysis

# 启用PostGIS扩展
psql -d gis_analysis -c "CREATE EXTENSION postgis;"
```

### 3. 环境变量配置

复制 `.env.example` 到 `.env` 并配置相关参数。

### 4. 运行应用

```bash
# 启动后端API服务
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# 启动前端开发服务器
cd frontend
npm install
npm start
```

## 📁 项目结构

```
├── app/                    # 后端应用
│   ├── api/               # API路由
│   ├── core/              # 核心配置
│   ├── models/            # 数据模型
│   ├── services/          # 业务逻辑
│   ├── ml/                # 机器学习模块
│   └── utils/             # 工具函数
├── frontend/              # 前端应用
├── data/                  # 数据文件
├── models/                # 训练好的模型
├── tests/                 # 测试文件
├── docker/                # Docker配置
└── docs/                  # 文档
```

## 🔧 开发指南

### 代码规范
- 使用 Black 进行代码格式化
- 使用 Flake8 进行代码检查
- 使用 MyPy 进行类型检查

### 测试
```bash
# 运行测试
pytest tests/

# 生成测试覆盖率报告
pytest --cov=app tests/
```

## 📊 模型性能

| 任务 | 准确率 | 处理速度 |
|------|--------|----------|
| 土地利用分类 | 95.2% | 2.3s/km² |
| 变化检测 | 92.8% | 3.1s/km² |
| 目标识别 | 89.5% | 1.8s/km² |

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

MIT License

## 📞 联系方式

如有问题或建议，请联系：[<EMAIL>]
