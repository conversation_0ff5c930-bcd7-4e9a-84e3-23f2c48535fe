"""
目标检测模型
"""
import torch
import torch.nn as nn
import numpy as np
from typing import Dict, Any, List

from app.ml.base_model import BaseModel


class ObjectDetector(BaseModel):
    """目标检测器"""
    
    def __init__(self, model_path: str = None):
        super().__init__(model_path)
        self.classes = ['background', 'building', 'road', 'vehicle', 'ship']
    
    def build_model(self) -> nn.Module:
        """构建目标检测模型"""
        # 简单的检测网络架构
        class SimpleDetector(nn.Module):
            def __init__(self, num_classes=5):
                super().__init__()
                self.backbone = nn.Sequential(
                    nn.Conv2d(3, 64, 3, padding=1),
                    nn.ReLU(),
                    nn.MaxPool2d(2),
                    nn.Conv2d(64, 128, 3, padding=1),
                    nn.ReLU(),
                    nn.MaxPool2d(2),
                    nn.Conv2d(128, 256, 3, padding=1),
                    nn.<PERSON>L<PERSON>(),
                    nn.MaxPool2d(2),
                    nn.Conv2d(256, 512, 3, padding=1),
                    nn.ReLU(),
                    nn.AdaptiveAvgPool2d((7, 7))
                )
                
                # 分类头
                self.classifier = nn.Sequential(
                    nn.Linear(512 * 7 * 7, 1024),
                    nn.ReLU(),
                    nn.Dropout(0.5),
                    nn.Linear(1024, num_classes)
                )
                
                # 回归头（简化的边界框回归）
                self.regressor = nn.Sequential(
                    nn.Linear(512 * 7 * 7, 1024),
                    nn.ReLU(),
                    nn.Dropout(0.5),
                    nn.Linear(1024, 4)  # x, y, w, h
                )
            
            def forward(self, x):
                features = self.backbone(x)
                features_flat = features.view(features.size(0), -1)
                
                class_scores = self.classifier(features_flat)
                bbox_coords = self.regressor(features_flat)
                
                return class_scores, bbox_coords
        
        return SimpleDetector(len(self.classes))
    
    def preprocess(self, image: np.ndarray) -> torch.Tensor:
        """预处理输入图像"""
        if image.dtype != np.uint8:
            image = (image * 255).astype(np.uint8)
        
        # 确保是3通道
        if image.ndim == 2:
            image = np.stack([image] * 3, axis=-1)
        elif image.shape[-1] == 1:
            image = np.repeat(image, 3, axis=-1)
        elif image.shape[-1] > 3:
            image = image[:, :, :3]
        
        # 转换为tensor
        tensor = torch.from_numpy(image).permute(2, 0, 1).float() / 255.0
        return tensor.unsqueeze(0).to(self.device)
    
    def postprocess(self, output) -> Dict[str, Any]:
        """后处理模型输出"""
        class_scores, bbox_coords = output
        
        # 应用softmax获取类别概率
        class_probs = torch.softmax(class_scores, dim=1)
        predicted_class = torch.argmax(class_probs, dim=1).item()
        confidence = class_probs[0, predicted_class].item()
        
        # 处理边界框坐标
        bbox = bbox_coords[0].cpu().numpy().tolist()
        
        # 获取所有类别的概率
        class_probabilities = {}
        for i, class_name in enumerate(self.classes):
            class_probabilities[class_name] = class_probs[0, i].item()
        
        return {
            'predicted_class_id': predicted_class,
            'predicted_class_name': self.classes[predicted_class],
            'confidence': confidence,
            'bbox': {
                'x': bbox[0],
                'y': bbox[1], 
                'width': bbox[2],
                'height': bbox[3]
            },
            'class_probabilities': class_probabilities,
            'detections': [
                {
                    'class_id': predicted_class,
                    'class_name': self.classes[predicted_class],
                    'confidence': confidence,
                    'bbox': bbox
                }
            ]
        }
