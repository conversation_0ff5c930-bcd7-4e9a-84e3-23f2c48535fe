"""
文件上传API端点
"""
import os
import uuid
from typing import List
from fastapi import APIRouter, UploadFile, File, HTTPException, Depends
from fastapi.responses import J<PERSON>NResponse

from app.core.config import settings, SUPPORTED_IMAGE_FORMATS
from app.utils.file_utils import validate_image_file, save_upload_file

router = APIRouter()


@router.post("/image", summary="上传遥感影像")
async def upload_image(
    file: UploadFile = File(..., description="遥感影像文件")
):
    """
    上传遥感影像文件
    
    支持的格式：.tif, .tiff, .jpg, .jpeg, .png, .bmp
    最大文件大小：100MB
    """
    try:
        # 验证文件格式
        if not validate_image_file(file.filename):
            raise HTTPException(
                status_code=400,
                detail=f"不支持的文件格式。支持的格式：{', '.join(SUPPORTED_IMAGE_FORMATS)}"
            )
        
        # 检查文件大小
        file_size = 0
        content = await file.read()
        file_size = len(content)
        
        if file_size > settings.MAX_FILE_SIZE * 1024 * 1024:  # 转换为字节
            raise HTTPException(
                status_code=400,
                detail=f"文件大小超过限制（{settings.MAX_FILE_SIZE}MB）"
            )
        
        # 生成唯一文件名
        file_id = str(uuid.uuid4())
        file_extension = os.path.splitext(file.filename)[1]
        new_filename = f"{file_id}{file_extension}"
        
        # 保存文件
        file_path = await save_upload_file(content, new_filename)
        
        return {
            "message": "文件上传成功",
            "file_id": file_id,
            "filename": file.filename,
            "file_path": file_path,
            "file_size": file_size,
            "status": "uploaded"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"文件上传失败：{str(e)}")


@router.post("/batch", summary="批量上传影像")
async def upload_batch_images(
    files: List[UploadFile] = File(..., description="多个遥感影像文件")
):
    """
    批量上传多个遥感影像文件
    """
    if len(files) > 10:  # 限制批量上传数量
        raise HTTPException(status_code=400, detail="批量上传文件数量不能超过10个")
    
    results = []
    failed_files = []
    
    for file in files:
        try:
            # 验证文件格式
            if not validate_image_file(file.filename):
                failed_files.append({
                    "filename": file.filename,
                    "error": f"不支持的文件格式"
                })
                continue
            
            # 检查文件大小
            content = await file.read()
            file_size = len(content)
            
            if file_size > settings.MAX_FILE_SIZE * 1024 * 1024:
                failed_files.append({
                    "filename": file.filename,
                    "error": f"文件大小超过限制（{settings.MAX_FILE_SIZE}MB）"
                })
                continue
            
            # 生成唯一文件名并保存
            file_id = str(uuid.uuid4())
            file_extension = os.path.splitext(file.filename)[1]
            new_filename = f"{file_id}{file_extension}"
            
            file_path = await save_upload_file(content, new_filename)
            
            results.append({
                "file_id": file_id,
                "filename": file.filename,
                "file_path": file_path,
                "file_size": file_size,
                "status": "uploaded"
            })
            
        except Exception as e:
            failed_files.append({
                "filename": file.filename,
                "error": str(e)
            })
    
    return {
        "message": f"批量上传完成，成功：{len(results)}个，失败：{len(failed_files)}个",
        "successful_uploads": results,
        "failed_uploads": failed_files,
        "total_files": len(files),
        "success_count": len(results),
        "failed_count": len(failed_files)
    }


@router.get("/status/{file_id}", summary="查询文件状态")
async def get_file_status(file_id: str):
    """
    查询上传文件的状态
    """
    # 这里可以查询数据库或文件系统来获取文件状态
    # 暂时返回模拟数据
    return {
        "file_id": file_id,
        "status": "uploaded",
        "message": "文件已上传，等待处理"
    }
