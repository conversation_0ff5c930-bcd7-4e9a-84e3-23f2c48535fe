"""
文件处理工具函数
"""
import os
import aiofiles
from pathlib import Path
from typing import Optional

from app.core.config import settings, SUPPORTED_IMAGE_FORMATS


def validate_image_file(filename: str) -> bool:
    """
    验证图像文件格式
    
    Args:
        filename: 文件名
        
    Returns:
        bool: 是否为支持的格式
    """
    if not filename:
        return False
    
    file_extension = os.path.splitext(filename)[1].lower()
    return file_extension in SUPPORTED_IMAGE_FORMATS


async def save_upload_file(content: bytes, filename: str) -> str:
    """
    保存上传的文件
    
    Args:
        content: 文件内容
        filename: 文件名
        
    Returns:
        str: 保存的文件路径
    """
    # 确保上传目录存在
    upload_dir = Path(settings.UPLOAD_DIR)
    upload_dir.mkdir(parents=True, exist_ok=True)
    
    # 构建完整文件路径
    file_path = upload_dir / filename
    
    # 异步写入文件
    async with aiofiles.open(file_path, 'wb') as f:
        await f.write(content)
    
    return str(file_path)


def get_file_info(file_path: str) -> Optional[dict]:
    """
    获取文件信息
    
    Args:
        file_path: 文件路径
        
    Returns:
        dict: 文件信息
    """
    if not os.path.exists(file_path):
        return None
    
    stat = os.stat(file_path)
    return {
        "path": file_path,
        "size": stat.st_size,
        "created_time": stat.st_ctime,
        "modified_time": stat.st_mtime,
        "extension": os.path.splitext(file_path)[1]
    }


def ensure_directory(directory: str) -> None:
    """
    确保目录存在
    
    Args:
        directory: 目录路径
    """
    Path(directory).mkdir(parents=True, exist_ok=True)


def get_safe_filename(filename: str) -> str:
    """
    获取安全的文件名（移除特殊字符）
    
    Args:
        filename: 原始文件名
        
    Returns:
        str: 安全的文件名
    """
    # 移除或替换不安全的字符
    safe_chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789.-_"
    safe_filename = "".join(c if c in safe_chars else "_" for c in filename)
    
    # 确保文件名不为空
    if not safe_filename or safe_filename.startswith('.'):
        safe_filename = "file" + safe_filename
    
    return safe_filename
