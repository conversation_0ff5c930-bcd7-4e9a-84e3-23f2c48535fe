"""
严格的依赖测试 - 验证所有导入和依赖
"""
import os
import sys
import importlib
import traceback

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_core_dependencies():
    """测试核心依赖"""
    dependencies = [
        'numpy',
        'PIL',
        'cv2',
        'torch',
        'torchvision', 
        'fastapi',
        'uvicorn',
        'pydantic',
        'aiofiles',
        'albumentations'
    ]
    
    failed_imports = []
    
    for dep in dependencies:
        try:
            importlib.import_module(dep)
            print(f"✅ {dep}")
        except ImportError as e:
            print(f"❌ {dep}: {str(e)}")
            failed_imports.append((dep, str(e)))
    
    if failed_imports:
        print(f"\n❌ 缺少 {len(failed_imports)} 个依赖:")
        for dep, error in failed_imports:
            print(f"   {dep}: {error}")
        return False
    
    print(f"\n✅ 所有 {len(dependencies)} 个核心依赖都可用")
    return True

def test_optional_dependencies():
    """测试可选依赖"""
    optional_deps = [
        'rasterio',
        'geopandas', 
        'shapely',
        'fiona',
        'pyproj',
        'gdal'
    ]
    
    available = []
    missing = []
    
    for dep in optional_deps:
        try:
            importlib.import_module(dep)
            available.append(dep)
            print(f"✅ {dep} (可选)")
        except ImportError:
            missing.append(dep)
            print(f"⚠️  {dep} (可选，未安装)")
    
    print(f"\n📊 可选依赖: {len(available)}/{len(optional_deps)} 可用")
    if missing:
        print(f"   缺少: {', '.join(missing)}")
    
    return True  # 可选依赖不影响测试结果

def test_app_imports():
    """测试应用模块导入"""
    modules = [
        'app.core.config',
        'app.main',
        'app.utils.file_utils',
        'app.utils.image_processing',
        'app.api.api_v1.api',
        'app.api.api_v1.endpoints.upload',
        'app.api.api_v1.endpoints.analysis',
        'app.api.api_v1.endpoints.models',
        'app.ml.base_model',
        'app.ml.land_use_classifier'
    ]
    
    failed_imports = []
    
    for module in modules:
        try:
            importlib.import_module(module)
            print(f"✅ {module}")
        except Exception as e:
            print(f"❌ {module}: {str(e)}")
            failed_imports.append((module, str(e)))
            # 打印详细错误信息
            print(f"   详细错误: {traceback.format_exc()}")
    
    if failed_imports:
        print(f"\n❌ {len(failed_imports)} 个模块导入失败:")
        for module, error in failed_imports:
            print(f"   {module}: {error}")
        return False
    
    print(f"\n✅ 所有 {len(modules)} 个应用模块导入成功")
    return True

def test_config_validation():
    """测试配置验证"""
    try:
        from app.core.config import settings, LAND_USE_CLASSES, SUPPORTED_IMAGE_FORMATS, MODEL_TYPES
        
        # 验证配置对象
        assert hasattr(settings, 'PROJECT_NAME')
        assert hasattr(settings, 'VERSION')
        assert hasattr(settings, 'DATABASE_URL')
        assert hasattr(settings, 'UPLOAD_DIR')
        
        # 验证常量
        assert isinstance(LAND_USE_CLASSES, dict)
        assert isinstance(SUPPORTED_IMAGE_FORMATS, list)
        assert isinstance(MODEL_TYPES, dict)
        
        print("✅ 配置验证通过")
        print(f"   项目名: {settings.PROJECT_NAME}")
        print(f"   版本: {settings.VERSION}")
        print(f"   土地类型: {len(LAND_USE_CLASSES)} 个")
        print(f"   支持格式: {len(SUPPORTED_IMAGE_FORMATS)} 个")
        
        return True
    except Exception as e:
        print(f"❌ 配置验证失败: {str(e)}")
        print(f"   详细错误: {traceback.format_exc()}")
        return False

def test_fastapi_app():
    """测试FastAPI应用"""
    try:
        from app.main import app
        from fastapi.testclient import TestClient
        
        # 创建测试客户端
        client = TestClient(app)
        
        # 测试健康检查
        response = client.get("/health")
        assert response.status_code == 200
        
        # 测试根路径
        response = client.get("/")
        assert response.status_code == 200
        
        print("✅ FastAPI应用测试通过")
        print(f"   应用标题: {app.title}")
        print(f"   路由数量: {len(app.routes)}")
        
        return True
    except Exception as e:
        print(f"❌ FastAPI应用测试失败: {str(e)}")
        print(f"   详细错误: {traceback.format_exc()}")
        return False

def test_image_processing():
    """测试图像处理功能"""
    try:
        from app.utils.image_processing import RemoteSensingProcessor
        import numpy as np
        
        # 创建处理器
        processor = RemoteSensingProcessor(target_size=(256, 256))
        
        # 创建测试图像
        test_image = np.random.randint(0, 255, (512, 512, 3), dtype=np.uint8)
        
        # 测试预处理
        processed = processor.preprocess_image(test_image)
        assert processed.shape == (256, 256, 3)
        
        # 测试分块
        patches = processor.create_patches(test_image, patch_size=(128, 128))
        assert len(patches) > 0
        
        print("✅ 图像处理功能测试通过")
        print(f"   处理器目标尺寸: {processor.target_size}")
        print(f"   测试图像分块数: {len(patches)}")
        
        return True
    except Exception as e:
        print(f"❌ 图像处理功能测试失败: {str(e)}")
        print(f"   详细错误: {traceback.format_exc()}")
        return False

def test_ml_models():
    """测试机器学习模型"""
    try:
        from app.ml.base_model import ModelManager
        from app.ml.land_use_classifier import LandUseClassifier
        
        # 测试模型管理器
        manager = ModelManager()
        available_models = manager.get_available_models()
        
        # 测试分类器创建
        classifier = LandUseClassifier()
        model_info = classifier.get_model_info()
        
        print("✅ 机器学习模型测试通过")
        print(f"   可用模型类型: {len(available_models)}")
        print(f"   分类器状态: {model_info['status']}")
        
        return True
    except Exception as e:
        print(f"❌ 机器学习模型测试失败: {str(e)}")
        print(f"   详细错误: {traceback.format_exc()}")
        return False

def main():
    """运行完整的依赖测试"""
    print("🔍 严格依赖测试")
    print("=" * 50)
    
    tests = [
        ("核心依赖", test_core_dependencies),
        ("可选依赖", test_optional_dependencies), 
        ("应用模块导入", test_app_imports),
        ("配置验证", test_config_validation),
        ("FastAPI应用", test_fastapi_app),
        ("图像处理", test_image_processing),
        ("机器学习模型", test_ml_models)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n📋 测试 {test_name}...")
        print("-" * 30)
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {str(e)}")
            print(f"   详细错误: {traceback.format_exc()}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 严格测试总结:")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅" if result else "❌"
        print(f"  {status} {test_name}")
    
    print(f"\n🎯 最终结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统依赖完整。")
    else:
        print("⚠️  存在问题，需要修复后才能继续开发。")
        print("\n🔧 建议修复步骤:")
        print("1. 安装缺失的依赖包")
        print("2. 检查模块导入错误")
        print("3. 验证配置文件正确性")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
