"""
模型管理API端点
"""
from typing import List, Optional
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel

from app.core.config import settings, MODEL_TYPES

router = APIRouter()


class ModelInfo(BaseModel):
    """模型信息模型"""
    model_id: str
    name: str
    type: str
    version: str
    description: str
    accuracy: float
    file_size: int
    created_at: str
    is_active: bool


@router.get("/", response_model=List[ModelInfo], summary="获取模型列表")
async def get_models(
    model_type: Optional[str] = None,
    is_active: Optional[bool] = None
):
    """
    获取可用的AI模型列表
    """
    # 模拟模型数据
    models = [
        ModelInfo(
            model_id="land_use_v1",
            name="土地利用分类模型 v1.0",
            type="land_use",
            version="1.0",
            description="基于ResNet50的土地利用分类模型，支持7类土地类型识别",
            accuracy=0.952,
            file_size=102400000,  # 100MB
            created_at="2024-01-01T00:00:00Z",
            is_active=True
        ),
        ModelInfo(
            model_id="change_detection_v1",
            name="变化检测模型 v1.0",
            type="change_detection",
            version="1.0",
            description="基于Siamese网络的变化检测模型",
            accuracy=0.928,
            file_size=87654321,  # 87MB
            created_at="2024-01-01T00:00:00Z",
            is_active=True
        ),
        ModelInfo(
            model_id="object_detection_v1",
            name="目标识别模型 v1.0",
            type="object_detection",
            version="1.0",
            description="基于YOLO v8的目标检测模型",
            accuracy=0.895,
            file_size=156789012,  # 156MB
            created_at="2024-01-01T00:00:00Z",
            is_active=True
        )
    ]
    
    # 应用过滤器
    if model_type:
        models = [m for m in models if m.type == model_type]
    if is_active is not None:
        models = [m for m in models if m.is_active == is_active]
    
    return models


@router.get("/{model_id}", response_model=ModelInfo, summary="获取模型详情")
async def get_model(model_id: str):
    """
    获取指定模型的详细信息
    """
    # 模拟查询模型
    if model_id == "land_use_v1":
        return ModelInfo(
            model_id="land_use_v1",
            name="土地利用分类模型 v1.0",
            type="land_use",
            version="1.0",
            description="基于ResNet50的土地利用分类模型，支持7类土地类型识别",
            accuracy=0.952,
            file_size=102400000,
            created_at="2024-01-01T00:00:00Z",
            is_active=True
        )
    else:
        raise HTTPException(status_code=404, detail="模型不存在")


@router.get("/{model_id}/performance", summary="获取模型性能指标")
async def get_model_performance(model_id: str):
    """
    获取模型的详细性能指标
    """
    if model_id not in ["land_use_v1", "change_detection_v1", "object_detection_v1"]:
        raise HTTPException(status_code=404, detail="模型不存在")
    
    # 模拟性能数据
    performance_data = {
        "land_use_v1": {
            "overall_accuracy": 0.952,
            "kappa_coefficient": 0.934,
            "class_accuracies": {
                "农田": 0.967,
                "森林": 0.943,
                "草地": 0.921,
                "水体": 0.989,
                "建筑用地": 0.956,
                "裸地": 0.912,
                "其他": 0.876
            },
            "confusion_matrix": [
                [967, 12, 8, 0, 13, 0, 0],
                [15, 943, 25, 0, 7, 10, 0],
                [18, 32, 921, 2, 15, 12, 0],
                [0, 0, 1, 989, 8, 2, 0],
                [8, 5, 12, 6, 956, 13, 0],
                [5, 15, 18, 3, 21, 912, 26],
                [2, 8, 12, 0, 18, 84, 876]
            ],
            "processing_speed": "2.3 seconds per km²",
            "memory_usage": "4.2 GB",
            "test_samples": 10000
        }
    }
    
    return performance_data.get(model_id, {})


@router.get("/types/available", summary="获取可用模型类型")
async def get_available_model_types():
    """
    获取系统支持的模型类型
    """
    return {
        "model_types": MODEL_TYPES,
        "total_count": len(MODEL_TYPES)
    }
