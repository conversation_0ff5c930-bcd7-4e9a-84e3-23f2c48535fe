"""
基础模型类
"""
import torch
import torch.nn as nn
import numpy as np
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, Tuple
from pathlib import Path

from app.core.config import settings


class BaseModel(ABC):
    """基础模型抽象类"""
    
    def __init__(self, model_path: Optional[str] = None):
        """
        初始化模型
        
        Args:
            model_path: 模型文件路径
        """
        self.model_path = model_path
        self.model = None
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.is_loaded = False
        
    @abstractmethod
    def build_model(self) -> nn.Module:
        """构建模型架构"""
        pass
    
    @abstractmethod
    def preprocess(self, image: np.ndarray) -> torch.Tensor:
        """预处理输入数据"""
        pass
    
    @abstractmethod
    def postprocess(self, output: torch.Tensor) -> Dict[str, Any]:
        """后处理模型输出"""
        pass
    
    def load_model(self, model_path: Optional[str] = None) -> None:
        """
        加载模型权重
        
        Args:
            model_path: 模型文件路径
        """
        if model_path:
            self.model_path = model_path
        
        if not self.model_path or not Path(self.model_path).exists():
            raise FileNotFoundError(f"模型文件不存在: {self.model_path}")
        
        # 构建模型
        self.model = self.build_model()
        
        # 加载权重
        checkpoint = torch.load(self.model_path, map_location=self.device)
        
        if isinstance(checkpoint, dict) and 'model_state_dict' in checkpoint:
            self.model.load_state_dict(checkpoint['model_state_dict'])
        else:
            self.model.load_state_dict(checkpoint)
        
        self.model.to(self.device)
        self.model.eval()
        self.is_loaded = True
        
        print(f"模型已加载: {self.model_path}")
    
    def predict(self, image: np.ndarray) -> Dict[str, Any]:
        """
        预测单张图像

        Args:
            image: 输入图像

        Returns:
            Dict: 预测结果
        """
        if not self.is_loaded or self.model is None:
            raise RuntimeError("模型未加载，请先调用load_model()")

        # 预处理
        input_tensor = self.preprocess(image)

        # 推理
        with torch.no_grad():
            output = self.model(input_tensor)

        # 后处理
        result = self.postprocess(output)

        return result
    
    def predict_batch(self, images: list) -> list:
        """
        批量预测
        
        Args:
            images: 图像列表
            
        Returns:
            list: 预测结果列表
        """
        results = []
        for image in images:
            result = self.predict(image)
            results.append(result)
        return results
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        if not self.is_loaded or self.model is None:
            return {"status": "not_loaded"}

        # 计算模型参数数量
        total_params = sum(p.numel() for p in self.model.parameters())
        trainable_params = sum(p.numel() for p in self.model.parameters() if p.requires_grad)
        
        return {
            "status": "loaded",
            "model_path": self.model_path,
            "device": str(self.device),
            "total_parameters": total_params,
            "trainable_parameters": trainable_params,
            "model_size_mb": Path(self.model_path).stat().st_size / (1024 * 1024) if self.model_path else 0
        }


class ModelManager:
    """模型管理器"""
    
    def __init__(self):
        self.models = {}
        self.model_configs = {
            'land_use': {
                'class': 'LandUseClassifier',
                'path': f"{settings.MODEL_DIR}/land_use_classification.pth",
                'description': '土地利用分类模型'
            },
            'change_detection': {
                'class': 'ChangeDetector',
                'path': f"{settings.MODEL_DIR}/change_detection.pth",
                'description': '变化检测模型'
            },
            'object_detection': {
                'class': 'ObjectDetector',
                'path': f"{settings.MODEL_DIR}/object_detection.pth",
                'description': '目标检测模型'
            }
        }
    
    def load_model(self, model_type: str) -> BaseModel:
        """
        加载指定类型的模型
        
        Args:
            model_type: 模型类型
            
        Returns:
            BaseModel: 加载的模型实例
        """
        if model_type in self.models:
            return self.models[model_type]
        
        if model_type not in self.model_configs:
            raise ValueError(f"不支持的模型类型: {model_type}")
        
        config = self.model_configs[model_type]
        
        # 动态导入模型类
        if model_type == 'land_use':
            from app.ml.land_use_classifier import LandUseClassifier
            model = LandUseClassifier(config['path'])
        elif model_type == 'change_detection':
            from app.ml.change_detector import ChangeDetector
            model = ChangeDetector(config['path'])
        elif model_type == 'object_detection':
            from app.ml.object_detector import ObjectDetector
            model = ObjectDetector(config['path'])
        else:
            raise ValueError(f"未实现的模型类型: {model_type}")
        
        # 尝试加载模型
        try:
            model.load_model()
            self.models[model_type] = model
            return model
        except Exception as e:
            print(f"模型加载失败 {model_type}: {str(e)}")
            # 返回未加载的模型实例，用于演示
            self.models[model_type] = model
            return model
    
    def get_available_models(self) -> Dict[str, Dict[str, Any]]:
        """获取可用模型列表"""
        available = {}
        for model_type, config in self.model_configs.items():
            model_path = Path(config['path'])
            available[model_type] = {
                'description': config['description'],
                'path': config['path'],
                'exists': model_path.exists(),
                'size_mb': model_path.stat().st_size / (1024 * 1024) if model_path.exists() else 0,
                'loaded': model_type in self.models
            }
        return available


# 全局模型管理器实例
model_manager = ModelManager()
