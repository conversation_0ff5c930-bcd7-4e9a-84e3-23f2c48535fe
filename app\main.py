"""
FastAPI应用主入口
"""
from fastapi import <PERSON><PERSON><PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse

from app.core.config import settings
from app.api.api_v1.api import api_router

# 创建FastAPI应用实例
app = FastAPI(
    title=settings.PROJECT_NAME,
    version=settings.VERSION,
    description=settings.DESCRIPTION,
    openapi_url=f"{settings.API_V1_STR}/openapi.json"
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.BACKEND_CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 挂载静态文件
app.mount("/static", StaticFiles(directory="static"), name="static")

# 注册API路由
app.include_router(api_router, prefix=settings.API_V1_STR)


@app.get("/", response_class=HTMLResponse)
async def root():
    """根路径，返回欢迎页面"""
    return """
    <!DOCTYPE html>
    <html>
    <head>
        <title>AI遥感智能分析系统</title>
        <meta charset="utf-8">
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
            .container { max-width: 800px; margin: 0 auto; background: white; padding: 40px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            h1 { color: #2c3e50; text-align: center; }
            .feature { margin: 20px 0; padding: 15px; background: #ecf0f1; border-radius: 5px; }
            .api-link { display: inline-block; margin: 10px; padding: 10px 20px; background: #3498db; color: white; text-decoration: none; border-radius: 5px; }
            .api-link:hover { background: #2980b9; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🛰️ AI遥感智能分析系统</h1>
            <p>欢迎使用基于深度学习的遥感影像智能分析系统！</p>
            
            <div class="feature">
                <h3>🌍 土地利用分类</h3>
                <p>自动识别农田、森林、水体、建筑用地等土地类型</p>
            </div>
            
            <div class="feature">
                <h3>🔍 变化检测</h3>
                <p>对比不同时期影像，检测土地利用变化</p>
            </div>
            
            <div class="feature">
                <h3>🎯 目标识别</h3>
                <p>识别建筑物、道路、车辆等特定目标</p>
            </div>
            
            <div style="text-align: center; margin-top: 30px;">
                <a href="/docs" class="api-link">📚 API文档</a>
                <a href="/redoc" class="api-link">📖 ReDoc文档</a>
            </div>
        </div>
    </body>
    </html>
    """


@app.get("/health")
async def health_check():
    """健康检查接口"""
    return {
        "status": "healthy",
        "service": settings.PROJECT_NAME,
        "version": settings.VERSION
    }


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.DEBUG
    )
