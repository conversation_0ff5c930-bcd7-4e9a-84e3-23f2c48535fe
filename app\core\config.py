"""
应用配置模块
"""
import os
from typing import List, Optional, Union

from pydantic_settings import BaseSettings
from pydantic import field_validator


class Settings(BaseSettings):
    """应用配置类"""
    
    # 基本配置
    PROJECT_NAME: str = "AI遥感智能分析系统"
    VERSION: str = "1.0.0"
    DESCRIPTION: str = "基于深度学习的遥感影像智能分析系统"
    API_V1_STR: str = "/api/v1"
    
    # 安全配置
    SECRET_KEY: str = "your-secret-key-change-in-production"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # 数据库配置
    DATABASE_URL: Optional[str] = None
    DATABASE_HOST: str = "localhost"
    DATABASE_PORT: int = 5432
    DATABASE_NAME: str = "gis_analysis"
    DATABASE_USER: str = "postgres"
    DATABASE_PASSWORD: str = "password"
    
    @field_validator("DATABASE_URL")
    @classmethod
    def assemble_db_connection(cls, v: Optional[str]) -> str:
        if isinstance(v, str):
            return v
        # 如果没有提供DATABASE_URL，使用默认值构建
        return (
            f"postgresql://postgres:password@localhost:5432/gis_analysis"
        )
    
    # Redis配置
    REDIS_URL: str = "redis://localhost:6379/0"
    REDIS_HOST: str = "localhost"
    REDIS_PORT: int = 6379
    REDIS_DB: int = 0
    
    # 文件存储配置
    UPLOAD_DIR: str = "./data/uploads"
    MODEL_DIR: str = "./models"
    RESULT_DIR: str = "./data/results"
    MAX_FILE_SIZE: int = 100  # MB
    
    # CORS配置
    BACKEND_CORS_ORIGINS: List[str] = [
        "http://localhost:3000",
        "http://localhost:8080",
        "http://localhost:8000"
    ]
    
    @field_validator("BACKEND_CORS_ORIGINS")
    @classmethod
    def assemble_cors_origins(cls, v: Union[str, List[str]]) -> List[str]:
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, list):
            return v
        elif isinstance(v, str):
            return [v]
        raise ValueError(v)
    
    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_FILE: str = "./logs/app.log"
    
    # 模型配置
    DEFAULT_MODEL_PATH: str = "./models/land_use_classification.pth"
    BATCH_SIZE: int = 16
    IMAGE_SIZE: int = 256
    NUM_CLASSES: int = 7
    
    # Celery配置
    CELERY_BROKER_URL: str = "redis://localhost:6379/0"
    CELERY_RESULT_BACKEND: str = "redis://localhost:6379/0"
    
    # 开发配置
    DEBUG: bool = True
    TESTING: bool = False
    
    class Config:
        env_file = ".env"
        case_sensitive = True


# 创建全局配置实例
settings = Settings()


# 土地利用分类标签
LAND_USE_CLASSES = {
    0: "农田",
    1: "森林", 
    2: "草地",
    3: "水体",
    4: "建筑用地",
    5: "裸地",
    6: "其他"
}

# 支持的文件格式
SUPPORTED_IMAGE_FORMATS = [
    ".tif", ".tiff", ".jpg", ".jpeg", ".png", ".bmp"
]

# 模型类型
MODEL_TYPES = {
    "land_use": "土地利用分类",
    "change_detection": "变化检测", 
    "object_detection": "目标识别"
}
