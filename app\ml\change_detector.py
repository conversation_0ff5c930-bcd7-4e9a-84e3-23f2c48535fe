"""
变化检测模型
"""
import torch
import torch.nn as nn
import numpy as np
from typing import Dict, Any

from app.ml.base_model import BaseModel


class ChangeDetector(BaseModel):
    """变化检测器"""
    
    def __init__(self, model_path: str = None):
        super().__init__(model_path)
    
    def build_model(self) -> nn.Module:
        """构建变化检测模型"""
        # 简单的Siamese网络架构
        class SiameseNet(nn.Module):
            def __init__(self):
                super().__init__()
                self.feature_extractor = nn.Sequential(
                    nn.Conv2d(3, 64, 3, padding=1),
                    nn.ReLU(),
                    nn.MaxPool2d(2),
                    nn.Conv2d(64, 128, 3, padding=1),
                    nn.ReLU(),
                    nn.MaxPool2d(2),
                    nn.Conv2d(128, 256, 3, padding=1),
                    nn.<PERSON>LU(),
                    nn.AdaptiveAvgPool2d((8, 8))
                )
                self.classifier = nn.Sequential(
                    nn.Linear(256 * 8 * 8 * 2, 512),
                    nn.ReLU(),
                    nn.Dropout(0.5),
                    nn.Linear(512, 2)  # 变化/无变化
                )
            
            def forward(self, x1, x2):
                feat1 = self.feature_extractor(x1)
                feat2 = self.feature_extractor(x2)
                feat1 = feat1.view(feat1.size(0), -1)
                feat2 = feat2.view(feat2.size(0), -1)
                combined = torch.cat([feat1, feat2], dim=1)
                return self.classifier(combined)
        
        return SiameseNet()
    
    def preprocess(self, image: np.ndarray) -> torch.Tensor:
        """预处理输入图像"""
        # 这里应该处理两张图像的输入
        # 暂时返回单张图像的处理结果
        if image.dtype != np.uint8:
            image = (image * 255).astype(np.uint8)
        
        # 转换为tensor
        tensor = torch.from_numpy(image).permute(2, 0, 1).float() / 255.0
        return tensor.unsqueeze(0).to(self.device)
    
    def postprocess(self, output: torch.Tensor) -> Dict[str, Any]:
        """后处理模型输出"""
        probabilities = torch.softmax(output, dim=1)
        predicted_class = torch.argmax(probabilities, dim=1).item()
        confidence = probabilities[0, predicted_class].item()
        
        return {
            'has_change': predicted_class == 1,
            'confidence': confidence,
            'change_probability': probabilities[0, 1].item(),
            'no_change_probability': probabilities[0, 0].item()
        }
