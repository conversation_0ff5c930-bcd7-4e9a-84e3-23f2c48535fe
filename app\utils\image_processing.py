"""
遥感影像处理工具函数
"""
import numpy as np
import cv2
from PIL import Image
import rasterio
from rasterio.windows import Window
from rasterio.transform import from_bounds
from typing import Tuple, Optional, List, Union
import albumentations as A


class RemoteSensingProcessor:
    """遥感影像处理器"""
    
    def __init__(self, target_size: Tuple[int, int] = (256, 256)):
        """
        初始化处理器
        
        Args:
            target_size: 目标图像尺寸 (height, width)
        """
        self.target_size = target_size
        self.augmentation_pipeline = self._create_augmentation_pipeline()
    
    def _create_augmentation_pipeline(self) -> <PERSON><PERSON>Compose:
        """创建数据增强管道"""
        return <PERSON><PERSON>([
            A.RandomRotate90(p=0.5),
            <PERSON><PERSON>(p=0.3),
            A<PERSON>Vertical<PERSON>lip(p=0.3),
            A.RandomBrightnessContrast(
                brightness_limit=0.2,
                contrast_limit=0.2,
                p=0.5
            ),
            <PERSON><PERSON>(var_limit=(10.0, 50.0), mean=0, p=0.3),
            <PERSON><PERSON>([
                <PERSON><PERSON>(p=0.2),
                <PERSON><PERSON>(blur_limit=3, p=0.1),
                <PERSON>.<PERSON>r(blur_limit=3, p=0.1),
            ], p=0.2),
        ])
    
    def read_image(self, file_path: str) -> Tuple[np.ndarray, dict]:
        """
        读取遥感影像
        
        Args:
            file_path: 影像文件路径
            
        Returns:
            tuple: (影像数组, 元数据)
        """
        try:
            # 尝试使用rasterio读取（支持多种遥感格式）
            with rasterio.open(file_path) as src:
                image = src.read()
                metadata = {
                    'crs': src.crs,
                    'transform': src.transform,
                    'width': src.width,
                    'height': src.height,
                    'count': src.count,
                    'dtype': src.dtypes[0],
                    'bounds': src.bounds
                }
                
                # 转换为 (H, W, C) 格式
                if image.ndim == 3:
                    image = np.transpose(image, (1, 2, 0))
                
                return image, metadata
                
        except Exception:
            # 如果rasterio失败，尝试使用PIL
            try:
                image = np.array(Image.open(file_path))
                metadata = {
                    'width': image.shape[1],
                    'height': image.shape[0],
                    'count': image.shape[2] if image.ndim == 3 else 1,
                    'dtype': str(image.dtype)
                }
                return image, metadata
            except Exception as e:
                raise ValueError(f"无法读取影像文件 {file_path}: {str(e)}")
    
    def preprocess_image(self, image: np.ndarray, normalize: bool = True) -> np.ndarray:
        """
        预处理影像
        
        Args:
            image: 输入影像数组
            normalize: 是否归一化
            
        Returns:
            np.ndarray: 预处理后的影像
        """
        # 确保图像是3通道
        if image.ndim == 2:
            image = np.stack([image] * 3, axis=-1)
        elif image.shape[-1] == 1:
            image = np.repeat(image, 3, axis=-1)
        elif image.shape[-1] > 3:
            # 如果是多光谱影像，选择前3个波段
            image = image[:, :, :3]
        
        # 调整尺寸
        if image.shape[:2] != self.target_size:
            image = cv2.resize(image, (self.target_size[1], self.target_size[0]))
        
        # 数据类型转换
        if image.dtype != np.uint8:
            # 将数据范围映射到0-255
            image = self._normalize_to_uint8(image)
        
        # 归一化到0-1
        if normalize:
            image = image.astype(np.float32) / 255.0
        
        return image
    
    def _normalize_to_uint8(self, image: np.ndarray) -> np.ndarray:
        """将影像归一化到uint8范围"""
        # 计算2%和98%分位数进行拉伸
        p2, p98 = np.percentile(image, (2, 98))
        image = np.clip(image, p2, p98)
        
        # 线性拉伸到0-255
        image = ((image - p2) / (p98 - p2) * 255).astype(np.uint8)
        return image
    
    def augment_image(self, image: np.ndarray) -> np.ndarray:
        """
        对影像进行数据增强
        
        Args:
            image: 输入影像
            
        Returns:
            np.ndarray: 增强后的影像
        """
        # 确保输入是uint8格式
        if image.dtype != np.uint8:
            image = (image * 255).astype(np.uint8)
        
        augmented = self.augmentation_pipeline(image=image)
        return augmented['image']
    
    def create_patches(
        self, 
        image: np.ndarray, 
        patch_size: Tuple[int, int] = (256, 256),
        overlap: float = 0.1
    ) -> List[Tuple[np.ndarray, Tuple[int, int]]]:
        """
        将大图像分割成小块
        
        Args:
            image: 输入影像
            patch_size: 块大小
            overlap: 重叠比例
            
        Returns:
            List: [(patch, (row, col)), ...]
        """
        patches = []
        h, w = image.shape[:2]
        patch_h, patch_w = patch_size
        
        # 计算步长
        step_h = int(patch_h * (1 - overlap))
        step_w = int(patch_w * (1 - overlap))
        
        for i in range(0, h - patch_h + 1, step_h):
            for j in range(0, w - patch_w + 1, step_w):
                patch = image[i:i+patch_h, j:j+patch_w]
                patches.append((patch, (i, j)))
        
        return patches
    
    def merge_patches(
        self,
        patches: List[Tuple[np.ndarray, Tuple[int, int]]],
        original_shape: Tuple[int, int],
        patch_size: Tuple[int, int] = (256, 256)
    ) -> np.ndarray:
        """
        将预测的小块合并成完整图像
        
        Args:
            patches: [(patch, (row, col)), ...]
            original_shape: 原始图像形状
            patch_size: 块大小
            
        Returns:
            np.ndarray: 合并后的图像
        """
        h, w = original_shape[:2]
        
        # 如果patches包含分类结果（单通道）
        if patches[0][0].ndim == 2:
            result = np.zeros((h, w), dtype=patches[0][0].dtype)
            count = np.zeros((h, w), dtype=np.int32)
        else:
            # 多通道结果
            c = patches[0][0].shape[-1]
            result = np.zeros((h, w, c), dtype=patches[0][0].dtype)
            count = np.zeros((h, w), dtype=np.int32)
        
        patch_h, patch_w = patch_size
        
        for patch, (i, j) in patches:
            end_i = min(i + patch_h, h)
            end_j = min(j + patch_w, w)
            
            if patch.ndim == 2:
                result[i:end_i, j:end_j] += patch[:end_i-i, :end_j-j]
                count[i:end_i, j:end_j] += 1
            else:
                result[i:end_i, j:end_j] += patch[:end_i-i, :end_j-j]
                count[i:end_i, j:end_j] += 1
        
        # 避免除零
        count[count == 0] = 1
        
        if result.ndim == 2:
            result = result / count
        else:
            result = result / count[:, :, np.newaxis]
        
        return result.astype(patches[0][0].dtype)


def calculate_ndvi(nir: np.ndarray, red: np.ndarray) -> np.ndarray:
    """
    计算归一化植被指数(NDVI)
    
    Args:
        nir: 近红外波段
        red: 红光波段
        
    Returns:
        np.ndarray: NDVI值
    """
    # 避免除零
    denominator = nir + red
    denominator[denominator == 0] = 1e-8
    
    ndvi = (nir - red) / denominator
    return np.clip(ndvi, -1, 1)


def calculate_ndwi(green: np.ndarray, nir: np.ndarray) -> np.ndarray:
    """
    计算归一化水体指数(NDWI)
    
    Args:
        green: 绿光波段
        nir: 近红外波段
        
    Returns:
        np.ndarray: NDWI值
    """
    denominator = green + nir
    denominator[denominator == 0] = 1e-8
    
    ndwi = (green - nir) / denominator
    return np.clip(ndwi, -1, 1)
