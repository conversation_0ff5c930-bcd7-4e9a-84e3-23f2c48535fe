"""
测试配置模块
"""
import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_config_import():
    """测试配置模块导入"""
    try:
        from app.core.config import settings, LAND_USE_CLASSES, SUPPORTED_IMAGE_FORMATS, MODEL_TYPES
        print("✅ 配置模块导入成功")
        return True
    except Exception as e:
        print(f"❌ 配置模块导入失败: {str(e)}")
        return False

def test_config_values():
    """测试配置值"""
    try:
        from app.core.config import settings, LAND_USE_CLASSES
        
        # 检查基本配置
        assert settings.PROJECT_NAME == "AI遥感智能分析系统"
        assert settings.VERSION == "1.0.0"
        assert settings.IMAGE_SIZE == 256
        assert len(LAND_USE_CLASSES) == 7
        
        print("✅ 配置值验证通过")
        print(f"   项目名称: {settings.PROJECT_NAME}")
        print(f"   版本: {settings.VERSION}")
        print(f"   图像尺寸: {settings.IMAGE_SIZE}")
        print(f"   土地类型数量: {len(LAND_USE_CLASSES)}")
        
        return True
    except Exception as e:
        print(f"❌ 配置值验证失败: {str(e)}")
        return False

def test_directories():
    """测试目录配置"""
    try:
        from app.core.config import settings
        
        # 创建必要目录
        directories = [
            settings.UPLOAD_DIR,
            settings.MODEL_DIR,
            settings.RESULT_DIR
        ]
        
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
            assert os.path.exists(directory)
        
        print("✅ 目录创建成功")
        for directory in directories:
            print(f"   {directory}")
        
        return True
    except Exception as e:
        print(f"❌ 目录创建失败: {str(e)}")
        return False

def main():
    """运行配置测试"""
    print("🔧 测试配置模块")
    print("=" * 40)
    
    tests = [
        ("配置导入", test_config_import),
        ("配置值验证", test_config_values),
        ("目录创建", test_directories)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}...")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}失败: {str(e)}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 40)
    print("📊 配置测试总结:")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅" if result else "❌"
        print(f"  {status} {test_name}")
    
    print(f"\n🎯 结果: {passed}/{total} 通过")
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
