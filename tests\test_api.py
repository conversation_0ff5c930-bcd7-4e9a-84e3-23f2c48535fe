"""
测试API模块
"""
import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_fastapi_import():
    """测试FastAPI导入"""
    try:
        from fastapi import FastAPI
        from fastapi.testclient import TestClient
        print("✅ FastAPI导入成功")
        return True
    except Exception as e:
        print(f"❌ FastAPI导入失败: {str(e)}")
        return False

def test_app_creation():
    """测试应用创建"""
    try:
        from app.main import app
        print("✅ 应用创建成功")
        print(f"   应用标题: {app.title}")
        print(f"   应用版本: {app.version}")
        return True
    except Exception as e:
        print(f"❌ 应用创建失败: {str(e)}")
        return False

def test_api_routes():
    """测试API路由"""
    try:
        from app.main import app
        
        # 获取所有路由
        routes = []
        for route in app.routes:
            if hasattr(route, 'path'):
                routes.append(route.path)
        
        # 检查关键路由
        expected_routes = ["/", "/health"]
        for route in expected_routes:
            if route not in routes:
                print(f"❌ 缺少路由: {route}")
                return False
        
        print("✅ API路由验证通过")
        print(f"   总路由数: {len(routes)}")
        for route in routes[:5]:  # 显示前5个路由
            print(f"   {route}")
        
        return True
    except Exception as e:
        print(f"❌ API路由验证失败: {str(e)}")
        return False

def test_health_endpoint():
    """测试健康检查端点"""
    try:
        from fastapi.testclient import TestClient
        from app.main import app
        
        client = TestClient(app)
        response = client.get("/health")
        
        assert response.status_code == 200
        data = response.json()
        assert "status" in data
        assert data["status"] == "healthy"
        
        print("✅ 健康检查端点测试通过")
        print(f"   响应状态: {response.status_code}")
        print(f"   响应数据: {data}")
        
        return True
    except Exception as e:
        print(f"❌ 健康检查端点测试失败: {str(e)}")
        return False

def test_root_endpoint():
    """测试根端点"""
    try:
        from fastapi.testclient import TestClient
        from app.main import app
        
        client = TestClient(app)
        response = client.get("/")
        
        assert response.status_code == 200
        assert "text/html" in response.headers["content-type"]
        
        print("✅ 根端点测试通过")
        print(f"   响应状态: {response.status_code}")
        print(f"   内容类型: {response.headers['content-type']}")
        
        return True
    except Exception as e:
        print(f"❌ 根端点测试失败: {str(e)}")
        return False

def main():
    """运行API测试"""
    print("🌐 测试API模块")
    print("=" * 40)
    
    tests = [
        ("FastAPI导入", test_fastapi_import),
        ("应用创建", test_app_creation),
        ("API路由", test_api_routes),
        ("健康检查端点", test_health_endpoint),
        ("根端点", test_root_endpoint)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}...")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}失败: {str(e)}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 40)
    print("📊 API测试总结:")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅" if result else "❌"
        print(f"  {status} {test_name}")
    
    print(f"\n🎯 结果: {passed}/{total} 通过")
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
