# 数据库配置
DATABASE_URL=postgresql://username:password@localhost:5432/gis_analysis
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=gis_analysis
DATABASE_USER=username
DATABASE_PASSWORD=password

# Redis配置
REDIS_URL=redis://localhost:6379/0
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0

# JWT配置
SECRET_KEY=your-secret-key-here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# 文件存储配置
UPLOAD_DIR=./data/uploads
MODEL_DIR=./models
RESULT_DIR=./data/results
MAX_FILE_SIZE=100  # MB

# API配置
API_V1_STR=/api/v1
PROJECT_NAME=AI遥感智能分析系统
VERSION=1.0.0
DESCRIPTION=基于深度学习的遥感影像智能分析系统

# CORS配置
BACKEND_CORS_ORIGINS=["http://localhost:3000", "http://localhost:8080"]

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=./logs/app.log

# 模型配置
DEFAULT_MODEL_PATH=./models/land_use_classification.pth
BATCH_SIZE=16
IMAGE_SIZE=256
NUM_CLASSES=7

# Celery配置
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# 邮件配置（可选）
SMTP_TLS=True
SMTP_PORT=587
SMTP_HOST=smtp.gmail.com
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-password

# 监控配置（可选）
SENTRY_DSN=your-sentry-dsn-here

# 开发模式
DEBUG=True
TESTING=False
