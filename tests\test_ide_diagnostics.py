"""
IDE诊断检查 - 验证所有Python文件没有类型错误
"""
import os
import sys
import ast
import importlib.util
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def find_python_files():
    """查找所有Python文件"""
    root_dir = Path(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    python_files = []
    
    for file_path in root_dir.rglob("*.py"):
        # 跳过测试文件和__pycache__
        if "__pycache__" in str(file_path) or file_path.name.startswith("test_"):
            continue
        python_files.append(file_path)
    
    return python_files

def check_syntax(file_path):
    """检查Python文件语法"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 尝试解析AST
        ast.parse(content)
        return True, None
    except SyntaxError as e:
        return False, f"语法错误: {e}"
    except Exception as e:
        return False, f"其他错误: {e}"

def check_imports(file_path):
    """检查文件导入"""
    try:
        # 获取相对于项目根目录的模块路径
        root_dir = Path(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        relative_path = file_path.relative_to(root_dir)
        
        # 转换为模块名
        module_name = str(relative_path.with_suffix('')).replace(os.sep, '.')
        
        # 尝试导入模块
        spec = importlib.util.spec_from_file_location(module_name, file_path)
        if spec and spec.loader:
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)
            return True, None
        else:
            return False, "无法创建模块规范"
            
    except ImportError as e:
        return False, f"导入错误: {e}"
    except Exception as e:
        return False, f"其他错误: {e}"

def main():
    """运行IDE诊断检查"""
    print("🔍 IDE诊断检查")
    print("=" * 50)
    
    # 查找所有Python文件
    python_files = find_python_files()
    print(f"📁 找到 {len(python_files)} 个Python文件")
    
    syntax_errors = []
    import_errors = []
    
    for file_path in python_files:
        relative_path = file_path.relative_to(Path.cwd())
        print(f"\n📋 检查 {relative_path}...")
        
        # 检查语法
        syntax_ok, syntax_error = check_syntax(file_path)
        if not syntax_ok:
            print(f"❌ 语法错误: {syntax_error}")
            syntax_errors.append((relative_path, syntax_error))
        else:
            print("✅ 语法正确")
        
        # 检查导入
        import_ok, import_error = check_imports(file_path)
        if not import_ok:
            print(f"❌ 导入错误: {import_error}")
            import_errors.append((relative_path, import_error))
        else:
            print("✅ 导入正确")
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 IDE诊断总结:")
    
    total_files = len(python_files)
    syntax_ok_count = total_files - len(syntax_errors)
    import_ok_count = total_files - len(import_errors)
    
    print(f"📁 总文件数: {total_files}")
    print(f"✅ 语法正确: {syntax_ok_count}/{total_files}")
    print(f"✅ 导入正确: {import_ok_count}/{total_files}")
    
    if syntax_errors:
        print(f"\n❌ 语法错误文件 ({len(syntax_errors)}):")
        for file_path, error in syntax_errors:
            print(f"   {file_path}: {error}")
    
    if import_errors:
        print(f"\n❌ 导入错误文件 ({len(import_errors)}):")
        for file_path, error in import_errors:
            print(f"   {file_path}: {error}")
    
    # 检查关键模块
    print(f"\n🔧 关键模块检查:")
    key_modules = [
        "app.core.config",
        "app.main", 
        "app.utils.file_utils",
        "app.utils.image_processing",
        "app.ml.base_model"
    ]
    
    key_module_errors = []
    for module_name in key_modules:
        try:
            importlib.import_module(module_name)
            print(f"✅ {module_name}")
        except Exception as e:
            print(f"❌ {module_name}: {e}")
            key_module_errors.append((module_name, str(e)))
    
    # 最终结果
    all_ok = (len(syntax_errors) == 0 and 
              len(import_errors) == 0 and 
              len(key_module_errors) == 0)
    
    print(f"\n🎯 最终结果: {'✅ 所有检查通过' if all_ok else '❌ 存在问题'}")
    
    if not all_ok:
        print("\n🔧 需要修复的问题:")
        if syntax_errors:
            print(f"   - {len(syntax_errors)} 个语法错误")
        if import_errors:
            print(f"   - {len(import_errors)} 个导入错误")
        if key_module_errors:
            print(f"   - {len(key_module_errors)} 个关键模块错误")
    
    return all_ok

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
