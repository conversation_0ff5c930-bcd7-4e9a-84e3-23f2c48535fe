"""
土地利用分类模型
"""
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Dict, Any
import torchvision.transforms as transforms
from torchvision.models import resnet50

from app.ml.base_model import BaseModel
from app.core.config import settings, LAND_USE_CLASSES


class LandUseClassifier(BaseModel):
    """土地利用分类器"""
    
    def __init__(self, model_path: str = None):
        super().__init__(model_path)
        self.num_classes = len(LAND_USE_CLASSES)
        self.image_size = settings.IMAGE_SIZE
        
        # 定义预处理变换
        self.transform = transforms.Compose([
            transforms.ToPILImage(),
            transforms.Resize((self.image_size, self.image_size)),
            transforms.ToTensor(),
            transforms.Normalize(
                mean=[0.485, 0.456, 0.406],  # ImageNet标准化参数
                std=[0.229, 0.224, 0.225]
            )
        ])
    
    def build_model(self) -> nn.Module:
        """构建基于ResNet50的分类模型"""
        # 使用预训练的ResNet50
        model = resnet50(pretrained=False)
        
        # 修改最后的全连接层
        model.fc = nn.Sequential(
            nn.Dropout(0.5),
            nn.Linear(model.fc.in_features, 512),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(512, self.num_classes)
        )
        
        return model
    
    def preprocess(self, image: np.ndarray) -> torch.Tensor:
        """
        预处理输入图像
        
        Args:
            image: 输入图像 (H, W, C)
            
        Returns:
            torch.Tensor: 预处理后的张量 (1, C, H, W)
        """
        # 确保图像是uint8格式
        if image.dtype != np.uint8:
            image = (image * 255).astype(np.uint8)
        
        # 确保是3通道
        if image.ndim == 2:
            image = np.stack([image] * 3, axis=-1)
        elif image.shape[-1] == 1:
            image = np.repeat(image, 3, axis=-1)
        elif image.shape[-1] > 3:
            image = image[:, :, :3]
        
        # 应用变换
        tensor = self.transform(image)
        
        # 添加batch维度
        tensor = tensor.unsqueeze(0).to(self.device)
        
        return tensor
    
    def postprocess(self, output: torch.Tensor) -> Dict[str, Any]:
        """
        后处理模型输出
        
        Args:
            output: 模型输出张量
            
        Returns:
            Dict: 处理后的结果
        """
        # 应用softmax获取概率
        probabilities = F.softmax(output, dim=1)
        
        # 获取预测类别
        predicted_class = torch.argmax(probabilities, dim=1).item()
        confidence = probabilities[0, predicted_class].item()
        
        # 获取所有类别的概率
        class_probabilities = {}
        for class_id, class_name in LAND_USE_CLASSES.items():
            class_probabilities[class_name] = probabilities[0, class_id].item()
        
        return {
            'predicted_class_id': predicted_class,
            'predicted_class_name': LAND_USE_CLASSES[predicted_class],
            'confidence': confidence,
            'class_probabilities': class_probabilities,
            'raw_output': output.cpu().numpy().tolist()
        }
    
    def predict_with_patches(self, image: np.ndarray, patch_size: int = 256, overlap: float = 0.1) -> Dict[str, Any]:
        """
        对大图像进行分块预测
        
        Args:
            image: 输入大图像
            patch_size: 块大小
            overlap: 重叠比例
            
        Returns:
            Dict: 预测结果
        """
        from app.utils.image_processing import RemoteSensingProcessor
        
        processor = RemoteSensingProcessor(target_size=(patch_size, patch_size))
        
        # 创建图像块
        patches = processor.create_patches(
            image, 
            patch_size=(patch_size, patch_size), 
            overlap=overlap
        )
        
        # 预测每个块
        patch_predictions = []
        class_counts = {class_name: 0 for class_name in LAND_USE_CLASSES.values()}
        
        for patch, (row, col) in patches:
            result = self.predict(patch)
            patch_predictions.append({
                'position': (row, col),
                'prediction': result
            })
            
            # 统计类别
            predicted_class = result['predicted_class_name']
            class_counts[predicted_class] += 1
        
        # 计算整体统计
        total_patches = len(patches)
        class_percentages = {
            class_name: (count / total_patches) * 100 
            for class_name, count in class_counts.items()
        }
        
        # 找到主要类别
        dominant_class = max(class_percentages, key=class_percentages.get)
        
        return {
            'analysis_type': 'patch_based_classification',
            'image_shape': image.shape,
            'total_patches': total_patches,
            'patch_size': patch_size,
            'overlap': overlap,
            'dominant_class': dominant_class,
            'class_statistics': class_percentages,
            'patch_predictions': patch_predictions[:10],  # 只返回前10个块的详细结果
            'summary': {
                'primary_land_use': dominant_class,
                'confidence': class_percentages[dominant_class],
                'diversity_index': len([p for p in class_percentages.values() if p > 5])  # 占比超过5%的类别数
            }
        }


def create_demo_model_file():
    """创建演示用的模型文件（随机权重）"""
    import os
    
    model_dir = settings.MODEL_DIR
    os.makedirs(model_dir, exist_ok=True)
    
    model_path = f"{model_dir}/land_use_classification.pth"
    
    if not os.path.exists(model_path):
        # 创建模型实例
        classifier = LandUseClassifier()
        model = classifier.build_model()
        
        # 保存随机初始化的权重（仅用于演示）
        torch.save(model.state_dict(), model_path)
        print(f"演示模型已创建: {model_path}")
    
    return model_path


# 如果直接运行此文件，创建演示模型
if __name__ == "__main__":
    create_demo_model_file()
