"""
测试图像处理模块
"""
import os
import sys
import numpy as np
from PIL import Image

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def create_test_image():
    """创建测试图像"""
    # 创建一个模拟的遥感图像
    image = np.random.randint(0, 255, (512, 512, 3), dtype=np.uint8)
    
    # 添加一些模式来模拟不同的土地类型
    # 左上角：森林（绿色）
    image[:256, :256, 1] = np.random.randint(100, 200, (256, 256))
    image[:256, :256, 0] = np.random.randint(20, 80, (256, 256))
    image[:256, :256, 2] = np.random.randint(20, 80, (256, 256))
    
    # 右上角：水体（蓝色）
    image[:256, 256:, 2] = np.random.randint(100, 200, (256, 256))
    image[:256, 256:, 0] = np.random.randint(20, 80, (256, 256))
    image[:256, 256:, 1] = np.random.randint(20, 80, (256, 256))
    
    return image

def test_image_processing_import():
    """测试图像处理模块导入"""
    try:
        from app.utils.image_processing import RemoteSensingProcessor
        print("✅ 图像处理模块导入成功")
        return True
    except Exception as e:
        print(f"❌ 图像处理模块导入失败: {str(e)}")
        return False

def test_processor_creation():
    """测试处理器创建"""
    try:
        from app.utils.image_processing import RemoteSensingProcessor
        
        processor = RemoteSensingProcessor(target_size=(256, 256))
        assert processor.target_size == (256, 256)
        
        print("✅ 处理器创建成功")
        print(f"   目标尺寸: {processor.target_size}")
        
        return True
    except Exception as e:
        print(f"❌ 处理器创建失败: {str(e)}")
        return False

def test_image_preprocessing():
    """测试图像预处理"""
    try:
        from app.utils.image_processing import RemoteSensingProcessor
        
        processor = RemoteSensingProcessor(target_size=(256, 256))
        test_image = create_test_image()
        
        # 测试预处理
        processed_image = processor.preprocess_image(test_image, normalize=True)
        
        assert processed_image.shape == (256, 256, 3)
        assert processed_image.dtype == np.float32
        assert 0 <= processed_image.min() <= processed_image.max() <= 1
        
        print("✅ 图像预处理测试通过")
        print(f"   原始尺寸: {test_image.shape}")
        print(f"   处理后尺寸: {processed_image.shape}")
        print(f"   数据类型: {processed_image.dtype}")
        print(f"   数值范围: [{processed_image.min():.3f}, {processed_image.max():.3f}]")
        
        return True
    except Exception as e:
        print(f"❌ 图像预处理测试失败: {str(e)}")
        return False

def test_image_patches():
    """测试图像分块"""
    try:
        from app.utils.image_processing import RemoteSensingProcessor
        
        processor = RemoteSensingProcessor()
        test_image = create_test_image()
        
        # 测试分块
        patches = processor.create_patches(
            test_image, 
            patch_size=(128, 128), 
            overlap=0.1
        )
        
        assert len(patches) > 0
        assert all(patch.shape[:2] == (128, 128) for patch, _ in patches)
        
        print("✅ 图像分块测试通过")
        print(f"   原始图像: {test_image.shape}")
        print(f"   块数量: {len(patches)}")
        print(f"   块尺寸: {patches[0][0].shape}")
        
        return True
    except Exception as e:
        print(f"❌ 图像分块测试失败: {str(e)}")
        return False

def test_image_save_load():
    """测试图像保存和加载"""
    try:
        from app.utils.image_processing import RemoteSensingProcessor
        
        processor = RemoteSensingProcessor()
        test_image = create_test_image()
        
        # 保存测试图像
        os.makedirs("data/uploads", exist_ok=True)
        test_image_path = "data/uploads/test_image.png"
        Image.fromarray(test_image).save(test_image_path)
        
        # 读取图像
        loaded_image, metadata = processor.read_image(test_image_path)
        
        assert loaded_image.shape == test_image.shape
        assert metadata['width'] == test_image.shape[1]
        assert metadata['height'] == test_image.shape[0]
        
        print("✅ 图像保存和加载测试通过")
        print(f"   保存路径: {test_image_path}")
        print(f"   加载尺寸: {loaded_image.shape}")
        print(f"   元数据: {metadata}")
        
        # 清理测试文件
        if os.path.exists(test_image_path):
            os.remove(test_image_path)
        
        return True
    except Exception as e:
        print(f"❌ 图像保存和加载测试失败: {str(e)}")
        return False

def main():
    """运行图像处理测试"""
    print("🖼️  测试图像处理模块")
    print("=" * 40)
    
    tests = [
        ("模块导入", test_image_processing_import),
        ("处理器创建", test_processor_creation),
        ("图像预处理", test_image_preprocessing),
        ("图像分块", test_image_patches),
        ("图像保存加载", test_image_save_load)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}...")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}失败: {str(e)}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 40)
    print("📊 图像处理测试总结:")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅" if result else "❌"
        print(f"  {status} {test_name}")
    
    print(f"\n🎯 结果: {passed}/{total} 通过")
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
